'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';
import { ref, uploadBytes } from 'firebase/storage';
import { db, storage } from '@/lib/firebase';

interface CreateChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  username: string;
  onChatCreated: (chatId: string) => void;
  editingChat?: {
    id: string;
    name: string;
    lastMessage: string;
    lastMessageTime: string;
    folder?: string;
  };
}

interface ChatData {
  name: string;
  systemPrompt: string;
  context: string;
  password: string;
  latexInstructions: boolean;
  latexAutomaticoEnviado: boolean;
  temperature: number;
  frequencyPenalty: number;
  repetitionPenalty: number;
  maxTokens: number;
}

type TabType = 'geral' | 'avancado';

interface SliderInputProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  min: number;
  max: number;
  step: number;
  description: string;
  icon: React.ReactNode;
}

const SliderInput = ({ label, value, onChange, min, max, step, description, icon }: SliderInputProps) => {
  const percentage = ((value - min) / (max - min)) * 100;

  const getStatus = () => {
    const middle = (min + max) / 2;
    if (value < middle * 0.8) return 'baixo';
    if (value > middle * 1.2) return 'alto';
    return 'medio';
  };

  const status = getStatus();

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-blue-300 flex items-center gap-2">
          {icon}
          {label}
        </label>
        <div className="flex items-center space-x-3">
          <span className="text-white/80 text-sm font-mono bg-white/10 px-3 py-1 rounded-lg">
            {value.toFixed(1)}
          </span>
          <div className="text-xs px-3 py-1 rounded-full bg-blue-500/20 text-blue-300 font-medium">
            {status === 'baixo' ? 'Baixo' : status === 'medio' ? 'Padrão' : 'Alto'}
          </div>
        </div>
      </div>

      <div className="relative py-4">
        {/* Linha do slider */}
        <div className="w-full h-1 bg-white/20 rounded-full relative">
          {/* Marcação do centro */}
          <div className="absolute left-1/2 top-0 bottom-0 w-0.5 bg-white/40 transform -translate-x-1/2" />
        </div>

        {/* Input range invisível */}
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(parseFloat(e.target.value))}
          className="absolute inset-0 w-full opacity-0 cursor-pointer z-10"
        />

        {/* Marcadores de valores */}
        <div className="absolute -bottom-2 left-0 right-0 flex justify-between text-xs text-white/40">
          <span>{min}</span>
          <span className="font-semibold text-blue-300">1.0</span>
          <span>{max}</span>
        </div>

        {/* Bola do slider */}
        <motion.div
          className="absolute top-1/2 w-6 h-6 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full transform -translate-y-1/2 -translate-x-1/2
                     shadow-lg shadow-blue-500/30 border-2 border-white/20 cursor-pointer"
          style={{ left: `${percentage}%` }}
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.9 }}
          transition={{ duration: 0.2 }}
        >
          <div className="absolute inset-1 bg-white/20 rounded-full" />
        </motion.div>
      </div>

      <p className="text-white/60 text-xs leading-relaxed">
        {description}
      </p>
    </div>
  );
};

export default function CreateChatModal({
  isOpen,
  onClose,
  username,
  onChatCreated,
  editingChat
}: CreateChatModalProps) {
  const [activeTab, setActiveTab] = useState<TabType>('geral');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const [chatData, setChatData] = useState<ChatData>({
    name: '',
    systemPrompt: '',
    context: '',
    password: '',
    latexInstructions: false,
    latexAutomaticoEnviado: false,
    temperature: 1.0,
    frequencyPenalty: 1.0,
    repetitionPenalty: 1.0,
    maxTokens: 2048
  });

  // Carregar dados do chat quando estiver editando
  useEffect(() => {
    if (editingChat && isOpen) {
      loadChatData();
    } else if (!editingChat && isOpen) {
      // Reset para valores padrão quando criar novo chat
      setChatData({
        name: '',
        systemPrompt: '',
        context: '',
        password: '',
        latexInstructions: false,
        latexAutomaticoEnviado: false,
        temperature: 1.0,
        frequencyPenalty: 1.0,
        repetitionPenalty: 1.0,
        maxTokens: 2048
      });
    }
  }, [editingChat, isOpen]);

  const loadChatData = async () => {
    if (!editingChat) return;

    try {
      const chatDoc = await getDoc(doc(db, 'usuarios', username, 'conversas', editingChat.id));
      if (chatDoc.exists()) {
        const data = chatDoc.data();
        setChatData({
          name: data.name || '',
          systemPrompt: data.systemPrompt || '',
          context: data.context || '',
          password: data.password || '',
          latexInstructions: data.latexInstructions || false,
          latexAutomaticoEnviado: data.latexAutomaticoEnviado || false,
          temperature: data.temperature || 1.0,
          frequencyPenalty: data.frequencyPenalty || 1.0,
          repetitionPenalty: data.repetitionPenalty || 1.0,
          maxTokens: data.maxTokens || 2048
        });
      }
    } catch (error) {
      console.error('Erro ao carregar dados do chat:', error);
      setError('Erro ao carregar dados do chat');
    }
  };

  const handleInputChange = (field: keyof ChatData, value: string | number | boolean) => {
    setChatData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateChatId = () => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `chat_${timestamp}_${random}`;
  };

  const createChat = async () => {
    if (!chatData.name.trim()) {
      setError('Nome do chat é obrigatório');
      return;
    }

    setLoading(true);
    setError('');

    try {
      if (editingChat) {
        // Modo edição - atualizar chat existente
        const now = new Date().toISOString();

        const updateData = {
          context: chatData.context,
          frequencyPenalty: chatData.frequencyPenalty,
          lastUpdatedAt: now,
          latexInstructions: chatData.latexInstructions,
          maxTokens: chatData.maxTokens,
          name: chatData.name,
          password: chatData.password,
          repetitionPenalty: chatData.repetitionPenalty,
          systemPrompt: chatData.systemPrompt,
          temperature: chatData.temperature,
          updatedAt: now
        };

        await updateDoc(doc(db, 'usuarios', username, 'conversas', editingChat.id), updateData);

        console.log('Chat atualizado com sucesso:', editingChat.id);
        onChatCreated(editingChat.id);
        onClose();

      } else {
        // Modo criação - criar novo chat
        const chatId = generateChatId();
        const now = new Date().toISOString();

        // Dados para o Firestore
        const firestoreData = {
          context: chatData.context,
          createdAt: now,
          folderId: null,
          frequencyPenalty: chatData.frequencyPenalty,
          isFixed: false,
          lastUpdatedAt: now,
          lastUsedModel: '',
          latexInstructions: chatData.latexInstructions,
          latexAutomaticoEnviado: chatData.latexAutomaticoEnviado,
          maxTokens: chatData.maxTokens,
          name: chatData.name,
          password: chatData.password,
          repetitionPenalty: chatData.repetitionPenalty,
          sessionTime: {
            lastSessionStart: null,
            lastUpdated: null,
            totalTime: 0
          },
          systemPrompt: chatData.systemPrompt,
          temperature: chatData.temperature,
          ultimaMensagem: '',
          ultimaMensagemEm: null,
          updatedAt: now
        };

        // Criar documento no Firestore
        await setDoc(doc(db, 'usuarios', username, 'conversas', chatId), firestoreData);

        // Criar arquivo chat.json no Storage
        const chatJsonData = {
          id: chatId,
          name: chatData.name,
          messages: [],
          createdAt: now,
          lastUpdated: now
        };

        const chatJsonBlob = new Blob([JSON.stringify(chatJsonData, null, 2)], {
          type: 'application/json'
        });

        const storageRef = ref(storage, `usuarios/${username}/conversas/${chatId}/chat.json`);
        await uploadBytes(storageRef, chatJsonBlob);

        console.log('Chat criado com sucesso:', chatId);
        onChatCreated(chatId);
        onClose();

        // Reset form
        setChatData({
          name: '',
          systemPrompt: '',
          context: '',
          password: '',
          latexInstructions: false,
          latexAutomaticoEnviado: false,
          temperature: 1.0,
          frequencyPenalty: 1.0,
          repetitionPenalty: 1.0,
          maxTokens: 2048
        });
      }

    } catch (error) {
      console.error('Erro ao processar chat:', error);
      setError(editingChat ? 'Erro ao atualizar conversa. Tente novamente.' : 'Erro ao criar conversa. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        {/* Background com efeito glassmorphism */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-gradient-to-br from-black/40 via-blue-900/30 to-purple-900/40 backdrop-blur-xl"
        />

        {/* Efeitos de partículas flutuantes */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-blue-400/20 rounded-full"
              initial={{
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                scale: 0
              }}
              animate={{
                y: [null, -100],
                scale: [0, 1, 0],
                opacity: [0, 0.6, 0]
              }}
              transition={{
                duration: Math.random() * 3 + 2,
                repeat: Infinity,
                delay: Math.random() * 2
              }}
            />
          ))}
        </div>

        <motion.div
          initial={{ scale: 0.8, opacity: 0, y: 50 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.8, opacity: 0, y: 50 }}
          transition={{
            type: "spring",
            duration: 0.6,
            bounce: 0.3
          }}
          className="relative bg-gradient-to-br from-slate-900/90 via-blue-900/90 to-indigo-900/90 backdrop-blur-2xl
                     border border-white/20 rounded-3xl w-full max-w-2xl max-h-[90vh] overflow-hidden
                     shadow-2xl shadow-blue-900/50"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Efeito de brilho no topo */}
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent" />

          {/* Efeito de brilho lateral */}
          <div className="absolute top-0 bottom-0 left-0 w-px bg-gradient-to-b from-transparent via-white/30 to-transparent" />
          <div className="absolute top-0 bottom-0 right-0 w-px bg-gradient-to-b from-transparent via-white/30 to-transparent" />
          {/* Header */}
          <motion.div
            className="relative p-6 border-b border-white/10 bg-gradient-to-r from-white/5 to-transparent"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="flex items-center space-x-4">
              <motion.div
                className="relative w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/30"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <motion.svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.4, type: "spring" }}
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </motion.svg>
                {/* Efeito de brilho */}
                <div className="absolute inset-0 bg-white/20 rounded-2xl blur-xl" />
              </motion.div>
              <div>
                <motion.h2
                  className="text-2xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  {editingChat ? 'Editar Conversa' : 'Nova Conversa'}
                </motion.h2>
                <motion.p
                  className="text-white/70 text-sm mt-1"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  {editingChat ? 'Altere as configurações da conversa' : 'Configure sua nova conversa com IA'}
                </motion.p>
              </div>
            </div>
            <motion.button
              onClick={onClose}
              className="absolute top-4 right-4 text-white/60 hover:text-white transition-all duration-300
                       p-3 hover:bg-white/10 rounded-2xl group"
              whileHover={{ scale: 1.1, rotate: 90 }}
              whileTap={{ scale: 0.9 }}
              transition={{ duration: 0.2 }}
            >
              <svg className="w-5 h-5 transition-transform duration-300 group-hover:rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </motion.button>
          </motion.div>

          {/* Tabs */}
          <motion.div
            className="flex bg-gradient-to-r from-white/10 to-white/5 mx-6 mt-6 rounded-2xl p-1 backdrop-blur-sm border border-white/10"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <motion.button
              onClick={() => setActiveTab('geral')}
              className={`flex-1 flex items-center justify-center space-x-2 px-6 py-4 rounded-xl transition-all duration-500 relative overflow-hidden ${
                activeTab === 'geral'
                  ? 'text-white shadow-xl'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {activeTab === 'geral' && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl"
                  layoutId="activeTab"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}
              <motion.svg
                className="w-5 h-5 relative z-10"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                animate={{ rotate: activeTab === 'geral' ? 360 : 0 }}
                transition={{ duration: 0.5 }}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </motion.svg>
              <span className="font-semibold relative z-10">Geral</span>
            </motion.button>
            <motion.button
              onClick={() => setActiveTab('avancado')}
              className={`flex-1 flex items-center justify-center space-x-2 px-6 py-4 rounded-xl transition-all duration-500 relative overflow-hidden ${
                activeTab === 'avancado'
                  ? 'text-white shadow-xl'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {activeTab === 'avancado' && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl"
                  layoutId="activeTab"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}
              <motion.svg
                className="w-5 h-5 relative z-10"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                animate={{ rotate: activeTab === 'avancado' ? 360 : 0 }}
                transition={{ duration: 0.5 }}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </motion.svg>
              <span className="font-semibold relative z-10">Avançado</span>
            </motion.button>
          </motion.div>

          {/* Conteúdo principal */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            <AnimatePresence mode="wait">
              {activeTab === 'geral' && (
                <motion.div
                  key="geral"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-5"
                >
                  {/* Nome do Chat */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium text-blue-300 flex items-center gap-2">
                        <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                        Nome do chat
                      </label>
                      <span className="text-red-400 text-xs bg-red-500/20 px-2 py-1 rounded-full">
                        obrigatório
                      </span>
                    </div>
                    <input
                      type="text"
                      value={chatData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="Ex: Projeto de física"
                      className="w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white
                               placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15
                               transition-all duration-300 hover:bg-white/15"
                    />
                    <p className="text-white/60 text-xs">
                      Nome obrigatório para identificar a conversa
                    </p>
                  </div>

                  {/* System Prompt */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium text-blue-300 flex items-center gap-2">
                        <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        System Prompt
                      </label>
                      <span className="text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full">opcional</span>
                    </div>
                    <textarea
                      value={chatData.systemPrompt}
                      onChange={(e) => handleInputChange('systemPrompt', e.target.value)}
                      placeholder="Instruções para o comportamento da IA..."
                      rows={3}
                      className="w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white
                               placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15
                               transition-all duration-300 hover:bg-white/15 resize-none"
                    />
                    <p className="text-white/60 text-xs">
                      Define como a IA deve se comportar e responder (ex: "Seja um assistente especializado em matemática")
                    </p>
                  </div>

                  {/* Contexto */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium text-blue-300 flex items-center gap-2">
                        <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                        Contexto
                      </label>
                      <span className="text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full">opcional</span>
                    </div>
                    <textarea
                      value={chatData.context}
                      onChange={(e) => handleInputChange('context', e.target.value)}
                      placeholder="Informações adicionais de contexto para a conversa..."
                      rows={3}
                      className="w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white
                               placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15
                               transition-all duration-300 hover:bg-white/15 resize-none"
                    />
                    <p className="text-white/60 text-xs">
                      Informações de fundo que a IA deve considerar durante toda a conversa
                    </p>
                  </div>

                  {/* Senha do Chat */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium text-blue-300 flex items-center gap-2">
                        <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" viewBox="0 0 24 24">
                          <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
                          <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                        </svg>
                        Senha do Chat
                      </label>
                      <span className="text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full">opcional</span>
                    </div>
                    <input
                      type="password"
                      value={chatData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      placeholder="Deixe vazio para chat sem proteção"
                      className="w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white
                               placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15
                               transition-all duration-300 hover:bg-white/15"
                    />
                    <p className="text-white/60 text-xs">
                      Se definida, será necessário inserir a senha para acessar este chat
                    </p>
                  </div>

                  {/* Instruções LaTeX */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-4 bg-white/5 rounded-2xl border border-white/10">
                      <div>
                        <h3 className="text-sm font-medium text-blue-300 flex items-center gap-2">
                          <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                          </svg>
                          Instruções LaTeX
                        </h3>
                        <p className="text-white/60 text-xs mt-1">Habilita formatação matemática avançada</p>
                      </div>
                      <button
                        onClick={() => handleInputChange('latexInstructions', !chatData.latexInstructions)}
                        className={`relative w-14 h-7 rounded-full transition-all duration-300 ${
                          chatData.latexInstructions
                            ? 'bg-gradient-to-r from-blue-500 to-cyan-500'
                            : 'bg-white/20'
                        }`}
                      >
                        <div
                          className={`absolute top-1 w-5 h-5 bg-white rounded-full shadow-lg transition-all duration-300 ${
                            chatData.latexInstructions ? 'left-8' : 'left-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>
                </motion.div>
              )}

              {activeTab === 'avancado' && (
                <motion.div
                  key="avancado"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6"
                >
                  {/* Temperature */}
                  <SliderInput
                    label="Temperatura"
                    value={chatData.temperature}
                    onChange={(value) => handleInputChange('temperature', value)}
                    min={0}
                    max={2}
                    step={0.1}
                    description="Controla a criatividade das respostas. Esquerda = mais preciso (0.1-0.8), Centro = balanceado (1.0), Direita = mais criativo (1.2-2.0)."
                    icon={
                      <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                        <path fillRule="evenodd" d="M12.963 2.286a.75.75 0 0 0-1.071-.136 9.742 9.742 0 0 0-3.539 6.176 7.547 7.547 0 0 1-1.705-1.715.75.75 0 0 0-1.152-.082A9 9 0 1 0 15.68 4.534a7.46 7.46 0 0 1-2.717-2.248ZM15.75 14.25a3.75 3.75 0 1 1-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 0 1 1.925-3.546 3.75 3.75 0 0 1 3.255 3.718Z" clipRule="evenodd" />
                      </svg>
                    }
                  />

                  {/* Frequency Penalty */}
                  <SliderInput
                    label="Frequency Penalty"
                    value={chatData.frequencyPenalty}
                    onChange={(value) => handleInputChange('frequencyPenalty', value)}
                    min={0}
                    max={2}
                    step={0.1}
                    description="Reduz repetição de palavras. Esquerda = sem penalidade (0.0-0.8), Centro = padrão (1.0), Direita = alta penalidade (1.2-2.0)."
                    icon={
                      <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    }
                  />

                  {/* Repetition Penalty */}
                  <SliderInput
                    label="Repetition Penalty"
                    value={chatData.repetitionPenalty}
                    onChange={(value) => handleInputChange('repetitionPenalty', value)}
                    min={0}
                    max={2}
                    step={0.1}
                    description="Penaliza tokens repetidos. Esquerda = sem penalidade (0.0-0.8), Centro = padrão (1.0), Direita = alta penalidade (1.2-2.0)."
                    icon={
                      <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    }
                  />

                  {/* Limite de Tokens */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium text-blue-300 flex items-center gap-2">
                        <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
                        </svg>
                        Limite de Tokens
                      </label>
                      <span className="text-white/80 text-sm font-mono bg-white/10 px-3 py-1 rounded-lg">
                        {chatData.maxTokens}
                      </span>
                    </div>

                    <input
                      type="number"
                      value={chatData.maxTokens}
                      onChange={(e) => handleInputChange('maxTokens', parseInt(e.target.value) || 2048)}
                      min={512}
                      max={8192}
                      step={256}
                      className="w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white
                               placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15
                               transition-all duration-300 hover:bg-white/15"
                    />

                    <p className="text-white/60 text-xs leading-relaxed">
                      Máximo de tokens que a IA pode gerar por resposta. Valores típicos: 512-4096.
                    </p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Error Message */}
            {error && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 text-sm"
              >
                {error}
              </motion.div>
            )}
          </div>

          {/* Footer */}
          <motion.div
            className="p-6 border-t border-white/10 bg-gradient-to-r from-white/5 to-transparent backdrop-blur-sm"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <div className="flex justify-end space-x-4">
              <motion.button
                onClick={onClose}
                disabled={loading}
                className="px-8 py-4 text-white/70 hover:text-white transition-all duration-500
                         hover:bg-white/10 rounded-2xl disabled:opacity-50 font-semibold
                         border border-white/20 hover:border-white/30 backdrop-blur-sm"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                Cancelar
              </motion.button>
              <motion.button
                onClick={createChat}
                disabled={loading || !chatData.name.trim()}
                className="px-10 py-4 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600
                         text-white rounded-2xl transition-all duration-500 disabled:opacity-50 disabled:cursor-not-allowed
                         flex items-center space-x-3 font-semibold shadow-xl shadow-blue-500/30 hover:shadow-blue-500/50
                         border border-blue-400/30 hover:border-blue-400/50 backdrop-blur-sm relative overflow-hidden"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                {/* Efeito de brilho */}
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />

                {loading ? (
                  <>
                    <motion.div
                      className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                    <span>{editingChat ? 'Salvando...' : 'Criando...'}</span>
                  </>
                ) : (
                  <>
                    <motion.svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      whileHover={{ rotate: editingChat ? 0 : 90 }}
                      transition={{ duration: 0.3 }}
                    >
                      {editingChat ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      ) : (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      )}
                    </motion.svg>
                    <span>{editingChat ? 'Salvar Alterações' : 'Criar Conversa'}</span>
                  </>
                )}
              </motion.button>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
