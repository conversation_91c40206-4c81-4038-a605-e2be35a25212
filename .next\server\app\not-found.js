/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Ccontexts%5CAppearanceContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Ccontexts%5CAuthContext.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Ccontexts%5CAppearanceContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Ccontexts%5CAuthContext.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AppearanceContext.tsx */ \"(ssr)/./src/contexts/AppearanceContext.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSGVucmklNUNEZXNrdG9wJTVDUmFmdGhvciU1Q1JhZnRob3JJQSU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hlbnJpJTVDRGVza3RvcCU1Q1JhZnRob3IlNUNSYWZ0aG9ySUElNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hlbnJpJTVDRGVza3RvcCU1Q1JhZnRob3IlNUNSYWZ0aG9ySUElNUNzcmMlNUNjb250ZXh0cyU1Q0FwcGVhcmFuY2VDb250ZXh0LnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hlbnJpJTVDRGVza3RvcCU1Q1JhZnRob3IlNUNSYWZ0aG9ySUElNUNzcmMlNUNjb250ZXh0cyU1Q0F1dGhDb250ZXh0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQXdIO0FBQ3hIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFmdGhvci8/Mjc2YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEhlbnJpXFxcXERlc2t0b3BcXFxcUmFmdGhvclxcXFxSYWZ0aG9ySUFcXFxcc3JjXFxcXGNvbnRleHRzXFxcXEFwcGVhcmFuY2VDb250ZXh0LnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSGVucmlcXFxcRGVza3RvcFxcXFxSYWZ0aG9yXFxcXFJhZnRob3JJQVxcXFxzcmNcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Ccontexts%5CAppearanceContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Ccontexts%5CAuthContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AppearanceContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/AppearanceContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppearanceProvider: () => (/* binding */ AppearanceProvider),\n/* harmony export */   useAppearance: () => (/* binding */ useAppearance)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useAppearance,AppearanceProvider auto */ \n\n\n\n\n// Configurações padrão\nconst DEFAULT_SETTINGS = {\n    fonte: \"Inter\",\n    tamanhoFonte: 14,\n    palavrasPorSessao: 5000,\n    sessionsEnabled: true,\n    latexAutomatico: false\n};\n// Criar contexto\nconst AppearanceContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Hook para usar o contexto\nconst useAppearance = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppearanceContext);\n    if (!context) {\n        throw new Error(\"useAppearance must be used within an AppearanceProvider\");\n    }\n    return context;\n};\n// Função para obter username do Firestore\nconst getUsernameFromFirestore = async (userEmail)=>{\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", userEmail);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const userData = userDoc.data();\n            return userData.username || userEmail;\n        }\n        return userEmail;\n    } catch (error) {\n        console.error(\"Erro ao obter username:\", error);\n        return userEmail;\n    }\n};\n// Provider do contexto\nconst AppearanceProvider = ({ children })=>{\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_SETTINGS);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Carregar configurações do Firestore\n    const loadSettings = async ()=>{\n        if (!user?.email) {\n            setSettings(DEFAULT_SETTINGS);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            setIsLoading(true);\n            const username = await getUsernameFromFirestore(user.email);\n            const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(configRef);\n            if (configDoc.exists()) {\n                const config = configDoc.data();\n                if (config.aparencia) {\n                    setSettings({\n                        fonte: config.aparencia.fonte || DEFAULT_SETTINGS.fonte,\n                        tamanhoFonte: config.aparencia.tamanhoFonte || DEFAULT_SETTINGS.tamanhoFonte,\n                        palavrasPorSessao: config.aparencia.palavrasPorSessao || DEFAULT_SETTINGS.palavrasPorSessao,\n                        sessionsEnabled: config.aparencia.sessionsEnabled !== undefined ? config.aparencia.sessionsEnabled : DEFAULT_SETTINGS.sessionsEnabled,\n                        latexAutomatico: config.aparencia.latexAutomatico !== undefined ? config.aparencia.latexAutomatico : DEFAULT_SETTINGS.latexAutomatico\n                    });\n                } else {\n                    setSettings(DEFAULT_SETTINGS);\n                }\n            } else {\n                setSettings(DEFAULT_SETTINGS);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            setSettings(DEFAULT_SETTINGS);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Carregar configurações quando o usuário mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n    }, [\n        user\n    ]);\n    // Aplicar configurações às variáveis CSS globais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading) {\n            const root = document.documentElement;\n            root.style.setProperty(\"--chat-font-family\", settings.fonte);\n            root.style.setProperty(\"--chat-font-size\", `${settings.tamanhoFonte}px`);\n            root.style.setProperty(\"--chat-words-per-session\", settings.palavrasPorSessao.toString());\n            root.style.setProperty(\"--chat-sessions-enabled\", settings.sessionsEnabled ? \"1\" : \"0\");\n            root.style.setProperty(\"--chat-latex-automatico\", settings.latexAutomatico ? \"1\" : \"0\");\n        }\n    }, [\n        settings,\n        isLoading\n    ]);\n    // Função para atualizar configurações\n    const updateSettings = async (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        setSettings(updatedSettings);\n        // Salvar no Firestore automaticamente\n        if (user) {\n            try {\n                const username = await getUsernameFromFirestore(user.email);\n                const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n                // Buscar configurações existentes\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(configRef);\n                const existingConfig = configDoc.exists() ? configDoc.data() : {};\n                // Atualizar apenas a seção de aparência\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(configRef, {\n                    ...existingConfig,\n                    aparencia: updatedSettings,\n                    updatedAt: new Date().toISOString()\n                }, {\n                    merge: true\n                });\n                console.log(\"✅ Configura\\xe7\\xf5es de apar\\xeancia salvas automaticamente\");\n                console.log(\"\\uD83D\\uDCCB Configura\\xe7\\xf5es salvas:\", updatedSettings);\n            } catch (error) {\n                console.error(\"❌ Erro ao salvar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            }\n        }\n    };\n    // Função para aplicar configurações a um elemento específico\n    const applyToElement = (element)=>{\n        element.style.fontFamily = settings.fonte;\n        element.style.fontSize = `${settings.tamanhoFonte}px`;\n    };\n    // Função para obter variáveis CSS como objeto\n    const getCSSVariables = ()=>{\n        return {\n            \"--chat-font-family\": settings.fonte,\n            \"--chat-font-size\": `${settings.tamanhoFonte}px`,\n            \"--chat-words-per-session\": settings.palavrasPorSessao.toString()\n        };\n    };\n    const value = {\n        settings,\n        updateSettings,\n        isLoading,\n        applyToElement,\n        getCSSVariables\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppearanceContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\contexts\\\\AppearanceContext.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AppearanceContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    loading: true,\n    logout: async ()=>{}\n});\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, (user)=>{\n            setUser(user);\n            setLoading(false);\n        });\n        return ()=>unsubscribe();\n    }, []);\n    const logout = async ()=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth);\n        } catch (error) {\n            console.error(\"Erro ao fazer logout:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        logout\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   functions: () => (/* binding */ functions),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n/* harmony import */ var firebase_functions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/functions */ \"(ssr)/./node_modules/firebase/functions/dist/index.mjs\");\n\n\n\n\n\n// Configuração do Firebase - novo app criado - ATUALIZADO\nconst firebaseConfig = {\n    apiKey: \"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8\",\n    authDomain: \"rafthor-0001.firebaseapp.com\",\n    projectId: \"rafthor-0001\",\n    storageBucket: \"rafthor-0001.firebasestorage.app\",\n    messagingSenderId: \"863587500028\",\n    appId: \"1:863587500028:web:ea161ddd3a1a024a7f3c79\"\n};\n// Verificar se a configuração está correta\nif (!firebaseConfig.apiKey || firebaseConfig.apiKey.length < 30) {\n    throw new Error(\"Firebase API Key inv\\xe1lida ou n\\xe3o configurada\");\n}\n// Inicializar Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Inicializar serviços\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\nconst functions = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_4__.getFunctions)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"aa15e3c3bbf4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFmdGhvci8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/OTIyNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImFhMTVlM2MzYmJmNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AppearanceContext */ \"(rsc)/./src/contexts/AppearanceContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Rafthor - AI Chatbot Platform\",\n    description: \"Uma plataforma de chatbot com m\\xfaltiplas IAs\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pt-BR\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__.AppearanceProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU1NQTtBQUpnQjtBQUMrQjtBQUNZO0FBSTFELE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdYLCtKQUFlO3NCQUM5Qiw0RUFBQ0MsK0RBQVlBOzBCQUNYLDRFQUFDQywyRUFBa0JBOzhCQUNoQks7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1iIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFmdGhvci8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcbmltcG9ydCB7IEFwcGVhcmFuY2VQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvQXBwZWFyYW5jZUNvbnRleHQnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdSYWZ0aG9yIC0gQUkgQ2hhdGJvdCBQbGF0Zm9ybScsXG4gIGRlc2NyaXB0aW9uOiAnVW1hIHBsYXRhZm9ybWEgZGUgY2hhdGJvdCBjb20gbcO6bHRpcGxhcyBJQXMnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwicHQtQlJcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPEF1dGhQcm92aWRlcj5cbiAgICAgICAgICA8QXBwZWFyYW5jZVByb3ZpZGVyPlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvQXBwZWFyYW5jZVByb3ZpZGVyPlxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIkF1dGhQcm92aWRlciIsIkFwcGVhcmFuY2VQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AppearanceContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/AppearanceContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppearanceProvider: () => (/* binding */ e1),
/* harmony export */   useAppearance: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Rafthor\RafthorIA\src\contexts\AppearanceContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = proxy["useAppearance"];

const e1 = proxy["AppearanceProvider"];


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Rafthor\RafthorIA\src\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = proxy["useAuth"];

const e1 = proxy["AuthProvider"];


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/idb","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();