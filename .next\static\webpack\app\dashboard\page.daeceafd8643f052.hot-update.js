"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n/* harmony import */ var _chatSessionsService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chatSessionsService */ \"(app-pages-browser)/./src/lib/services/chatSessionsService.ts\");\n// Serviço para integração com Firebase Functions de IA\n\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model,\n                attachments: config.attachments || [],\n                isRegeneration: config.isRegeneration || false,\n                webSearchEnabled: config.webSearchEnabled || false,\n                userMessageId: config.userMessageId\n            };\n            console.log(\"=== DEBUG: AI SERVICE REQUEST DATA ===\");\n            console.log(\"Request data:\", JSON.stringify(requestData, null, 2));\n            console.log(\"Attachments length:\", requestData.attachments.length);\n            if (requestData.attachments.length > 0) {\n                console.log(\"First attachment:\", JSON.stringify(requestData.attachments[0], null, 2));\n            }\n            console.log(\"\\uD83D\\uDD0D Username:\", requestData.username);\n            console.log(\"\\uD83D\\uDD0D ChatId:\", requestData.chatId);\n            console.log(\"\\uD83D\\uDD0D Message:\", requestData.message);\n            // Enviar requisição\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            // Processar stream\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    return;\n                }\n                onError(error.message);\n            } else {\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtém contexto completo do chat para envio à IA\n   * IMPORTANTE: Sempre usa TODAS as mensagens, independente das sessões\n   */ async getFullChatContextForAI(chatId) {\n        try {\n            // Tenta obter contexto do sistema de sessões primeiro\n            const context = await _chatSessionsService__WEBPACK_IMPORTED_MODULE_0__.chatSessionsService.getFullChatContext(chatId);\n            if (context && context.allMessages.length > 0) {\n                // Converte mensagens do formato interno para formato da IA\n                return this.convertToAIFormat(context.allMessages);\n            }\n            // Fallback: se não há sessões, retorna array vazio\n            // O contexto será construído normalmente pelo carregamento direto\n            return [];\n        } catch (error) {\n            console.warn(\"Sistema de sess\\xf5es n\\xe3o dispon\\xedvel, usando carregamento direto:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        try {\n            this.validateConfig(config);\n            await this.sendMessage(config, onChunk, onComplete, onError);\n        } catch (error) {\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});