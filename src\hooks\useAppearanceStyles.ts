import { useAppearance } from '@/contexts/AppearanceContext';
import { CSSProperties } from 'react';

/**
 * Hook personalizado para obter estilos de aparência aplicáveis
 */
export const useAppearanceStyles = () => {
  const { settings } = useAppearance();

  // Estilos base para texto do chat
  const chatTextStyles: CSSProperties = {
    fontFamily: settings.fonte,
    fontSize: `${settings.tamanhoFonte}px`,
  };

  // Estilos para inputs e textareas
  const inputStyles: CSSProperties = {
    fontFamily: settings.fonte,
    fontSize: `${settings.tamanhoFonte}px`,
  };

  // Estilos para pré-visualização
  const previewStyles: CSSProperties = {
    fontFamily: settings.fonte,
    fontSize: `${settings.tamanhoFonte}px`,
    lineHeight: 1.7,
  };

  // Função para aplicar estilos a um elemento
  const applyToElement = (element: HTMLElement) => {
    element.style.fontFamily = settings.fonte;
    element.style.fontSize = `${settings.tamanhoFonte}px`;
  };

  // Função para obter variáveis CSS
  const getCSSVariables = () => ({
    '--chat-font-family': settings.fonte,
    '--chat-font-size': `${settings.tamanhoFonte}px`,
    '--chat-words-per-session': settings.palavrasPorSessao.toString(),
    '--chat-latex-automatico': settings.latexAutomatico ? '1' : '0',
  });

  return {
    settings,
    chatTextStyles,
    inputStyles,
    previewStyles,
    applyToElement,
    getCSSVariables,
  };
};
