"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/contexts/AppearanceContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/AppearanceContext.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppearanceProvider: function() { return /* binding */ AppearanceProvider; },\n/* harmony export */   useAppearance: function() { return /* binding */ useAppearance; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useAppearance,AppearanceProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Configurações padrão\nconst DEFAULT_SETTINGS = {\n    fonte: \"Inter\",\n    tamanhoFonte: 14,\n    palavrasPorSessao: 5000,\n    sessionsEnabled: true,\n    latexAutomatico: false\n};\n// Criar contexto\nconst AppearanceContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Hook para usar o contexto\nconst useAppearance = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppearanceContext);\n    if (!context) {\n        throw new Error(\"useAppearance must be used within an AppearanceProvider\");\n    }\n    return context;\n};\n_s(useAppearance, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Função para obter username do Firestore\nconst getUsernameFromFirestore = async (userEmail)=>{\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", userEmail);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const userData = userDoc.data();\n            return userData.username || userEmail;\n        }\n        return userEmail;\n    } catch (error) {\n        console.error(\"Erro ao obter username:\", error);\n        return userEmail;\n    }\n};\n// Provider do contexto\nconst AppearanceProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_SETTINGS);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Carregar configurações do Firestore\n    const loadSettings = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) {\n            setSettings(DEFAULT_SETTINGS);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            setIsLoading(true);\n            const username = await getUsernameFromFirestore(user.email);\n            const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(configRef);\n            if (configDoc.exists()) {\n                const config = configDoc.data();\n                if (config.aparencia) {\n                    setSettings({\n                        fonte: config.aparencia.fonte || DEFAULT_SETTINGS.fonte,\n                        tamanhoFonte: config.aparencia.tamanhoFonte || DEFAULT_SETTINGS.tamanhoFonte,\n                        palavrasPorSessao: config.aparencia.palavrasPorSessao || DEFAULT_SETTINGS.palavrasPorSessao,\n                        sessionsEnabled: config.aparencia.sessionsEnabled !== undefined ? config.aparencia.sessionsEnabled : DEFAULT_SETTINGS.sessionsEnabled,\n                        latexAutomatico: config.aparencia.latexAutomatico !== undefined ? config.aparencia.latexAutomatico : DEFAULT_SETTINGS.latexAutomatico\n                    });\n                } else {\n                    setSettings(DEFAULT_SETTINGS);\n                }\n            } else {\n                setSettings(DEFAULT_SETTINGS);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            setSettings(DEFAULT_SETTINGS);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Carregar configurações quando o usuário mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n    }, [\n        user\n    ]);\n    // Aplicar configurações às variáveis CSS globais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading) {\n            const root = document.documentElement;\n            root.style.setProperty(\"--chat-font-family\", settings.fonte);\n            root.style.setProperty(\"--chat-font-size\", \"\".concat(settings.tamanhoFonte, \"px\"));\n            root.style.setProperty(\"--chat-words-per-session\", settings.palavrasPorSessao.toString());\n            root.style.setProperty(\"--chat-sessions-enabled\", settings.sessionsEnabled ? \"1\" : \"0\");\n            root.style.setProperty(\"--chat-latex-automatico\", settings.latexAutomatico ? \"1\" : \"0\");\n        }\n    }, [\n        settings,\n        isLoading\n    ]);\n    // Função para atualizar configurações\n    const updateSettings = async (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        setSettings(updatedSettings);\n        // Salvar no Firestore automaticamente\n        if (user) {\n            try {\n                const username = await getUsernameFromFirestore(user.email);\n                const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n                // Buscar configurações existentes\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(configRef);\n                const existingConfig = configDoc.exists() ? configDoc.data() : {};\n                // Atualizar apenas a seção de aparência\n                const dataToSave = {\n                    ...existingConfig,\n                    aparencia: updatedSettings,\n                    updatedAt: new Date().toISOString()\n                };\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(configRef, dataToSave, {\n                    merge: true\n                });\n                console.log(\"✅ Configura\\xe7\\xf5es de apar\\xeancia salvas automaticamente\");\n                console.log(\"\\uD83D\\uDCCB Configura\\xe7\\xf5es salvas:\", updatedSettings);\n                console.log(\"\\uD83D\\uDCCB Estrutura completa salva:\", dataToSave);\n            } catch (error) {\n                console.error(\"❌ Erro ao salvar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            }\n        }\n    };\n    // Função para aplicar configurações a um elemento específico\n    const applyToElement = (element)=>{\n        element.style.fontFamily = settings.fonte;\n        element.style.fontSize = \"\".concat(settings.tamanhoFonte, \"px\");\n    };\n    // Função para obter variáveis CSS como objeto\n    const getCSSVariables = ()=>{\n        return {\n            \"--chat-font-family\": settings.fonte,\n            \"--chat-font-size\": \"\".concat(settings.tamanhoFonte, \"px\"),\n            \"--chat-words-per-session\": settings.palavrasPorSessao.toString()\n        };\n    };\n    const value = {\n        settings,\n        updateSettings,\n        isLoading,\n        applyToElement,\n        getCSSVariables\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppearanceContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\contexts\\\\AppearanceContext.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AppearanceProvider, \"GTB8At3kv228hKHqctGDrslQ74E=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = AppearanceProvider;\nvar _c;\n$RefreshReg$(_c, \"AppearanceProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AppearanceContext.tsx\n"));

/***/ })

});