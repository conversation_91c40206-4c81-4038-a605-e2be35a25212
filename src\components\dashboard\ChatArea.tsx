'use client';

import { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { doc, setDoc, collection, getDoc, updateDoc, query, where, getDocs } from 'firebase/firestore';
import { ref as storageRef, uploadBytes } from 'firebase/storage';
import { db, storage } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { useAppearance } from '@/contexts/AppearanceContext';

import ChatInterface from './ChatInterface';
import InputBar from './InputBar';
import AttachmentDisplay from './AttachmentDisplay';
import DownloadModal from './DownloadModal';
import ModelSelectionModal from './ModelSelectionModal';
import AttachmentsModal from './AttachmentsModal';
import StatisticsModal from './StatisticsModal';
import { ChatMessage } from '@/lib/types/chat';
import aiService from '@/lib/services/aiService';

interface WebSearchAnnotation {
  type: "url_citation";
  url: string;
  title: string;
  content?: string;
  start_index: number;
  end_index: number;
}

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: string;
  isFavorite?: boolean;
  attachments?: import('@/lib/types/chat').AttachmentMetadata[];
  hasWebSearch?: boolean;
  webSearchAnnotations?: WebSearchAnnotation[];
}

interface ChatAreaProps {
  currentChat: string | null;
  onChatCreated?: (chatId: string) => void;
  onUpdateOpenRouterBalance?: () => void;
  onChatSelect?: (chatId: string) => void;
  sidebarRef?: React.RefObject<{ reloadChats: () => void; updateOpenRouterBalance: () => void; moveToTop: (chatId: string) => void }>;
}

const ChatArea = forwardRef<{
  handleDownloadModal: (chatId?: string) => void;
  handleAttachmentsModal: (chatId?: string) => void;
  handleStatisticsModal: (chatId?: string) => void;
}, ChatAreaProps>(({ currentChat, onChatCreated, onUpdateOpenRouterBalance, onChatSelect, sidebarRef }, ref) => {
  const { user } = useAuth();
  const { settings: appearanceSettings } = useAppearance();
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedModel, setSelectedModel] = useState('meta-llama/llama-3.1-8b-instruct:free');

  const [actualChatId, setActualChatId] = useState<string | null>(currentChat);
  const [isDownloadModalOpen, setIsDownloadModalOpen] = useState(false);
  const [isModelModalOpen, setIsModelModalOpen] = useState(false);
  const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = useState(false);
  const [isStatisticsModalOpen, setIsStatisticsModalOpen] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null);
  const [chatName, setChatName] = useState<string>('Nova Conversa');


  const [isLoadingChat, setIsLoadingChat] = useState(false);
  const [currentUsername, setCurrentUsername] = useState<string | undefined>(undefined);
  const chatInterfaceRef = useRef<HTMLDivElement>(null);

  // Estados para drag-n-drop
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragCounter, setDragCounter] = useState(0);

  // Carregar username quando o usuário estiver disponível
  useEffect(() => {
    const loadUsername = async () => {
      if (user?.email) {
        const username = await getUsernameFromFirestore();
        setCurrentUsername(username);
      }
    };
    loadUsername();
  }, [user?.email]);

  // Função utilitária para buscar username correto
  const getUsernameFromFirestore = async (): Promise<string> => {
    if (!user?.email) return 'unknown';

    try {
      const usuariosRef = collection(db, 'usuarios');
      const q = query(usuariosRef, where('email', '==', user.email));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const userDoc = querySnapshot.docs[0];
        const userData = userDoc.data();
        return userData.username || user.email.split('@')[0];
      }

      return user.email.split('@')[0]; // fallback
    } catch (error) {
      console.error('Erro ao buscar username:', error);
      return user.email.split('@')[0]; // fallback
    }
  };

  // Função para salvar o último modelo usado para um chat específico
  const saveLastUsedModelForChat = async (modelId: string, chatId: string) => {
    if (!user || !chatId) return;

    try {
      const username = await getUsernameFromFirestore();
      const chatRef = doc(db, 'usuarios', username, 'conversas', chatId);

      // Atualizar o lastUsedModel no documento do chat
      await updateDoc(chatRef, {
        lastUsedModel: modelId,
        lastModelUpdateAt: Date.now()
      });


    } catch (error) {
      console.error('Error saving last used model for chat:', error);
    }
  };

  // Função para carregar o último modelo usado de um chat específico
  const loadLastUsedModelForChat = async (chatId: string) => {
    if (!user || !chatId) return;

    try {
      const username = await getUsernameFromFirestore();
      const chatRef = doc(db, 'usuarios', username, 'conversas', chatId);
      const chatDoc = await getDoc(chatRef);

      if (chatDoc.exists()) {
        const data = chatDoc.data();
        if (data.lastUsedModel) {
          // Verificar se o modelo salvo ainda é válido
          const isValid = await isValidModel(data.lastUsedModel);
          if (isValid) {
            setSelectedModel(data.lastUsedModel);

          } else {

            // Limpar o modelo inválido do chat
            await updateDoc(chatRef, {
              lastUsedModel: ''
            });
            // Carregar o modelo padrão do endpoint ativo
            loadDefaultModelFromActiveEndpoint();
          }
        } else {
          // Se o chat não tem modelo salvo, carregar o modelo padrão do endpoint ativo
          loadDefaultModelFromActiveEndpoint();

        }
      }
    } catch (error) {
      console.error('Error loading last used model for chat:', error);
    }
  };

  // Função para carregar o modelo padrão do endpoint ativo
  const loadDefaultModelFromActiveEndpoint = async () => {
    if (!user) return;

    try {
      const username = await getUsernameFromFirestore();
      const userRef = doc(db, 'usuarios', username, 'configuracoes', 'settings');
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        const data = userDoc.data();

        // Primeiro, tentar carregar o último modelo usado globalmente
        if (data.lastUsedModel) {
          setSelectedModel(data.lastUsedModel);

          return;
        }

        // Se não há último modelo usado, buscar o modelo padrão do endpoint ativo
        if (data.endpoints) {
          const activeEndpoint = Object.values(data.endpoints).find((endpoint: any) => endpoint.ativo);
          if (activeEndpoint && (activeEndpoint as any).modeloPadrao) {
            setSelectedModel((activeEndpoint as any).modeloPadrao);

            return;
          }
        }
      }

      // Fallback para o modelo padrão hardcoded
      setSelectedModel('meta-llama/llama-3.1-8b-instruct:free');

    } catch (error) {
      console.error('Error loading default model from active endpoint:', error);
      // Fallback para o modelo padrão hardcoded em caso de erro
      setSelectedModel('meta-llama/llama-3.1-8b-instruct:free');
    }
  };

  // Função para validar se um modelo ainda existe/é válido
  const isValidModel = async (modelId: string): Promise<boolean> => {
    // Lista de modelos conhecidos como inválidos ou removidos
    const invalidModels = [
      'qwen/qwen3-235b-a22b-thinking-2507',
      // Adicione outros modelos inválidos aqui conforme necessário
    ];

    return !invalidModels.includes(modelId);
  };

  // Função para limpar modelos inválidos de todos os chats do usuário
  const cleanupInvalidModelsFromAllChats = async () => {
    if (!user) return;

    try {
      const username = await getUsernameFromFirestore();
      const chatsRef = collection(db, 'usuarios', username, 'conversas');
      const chatsSnapshot = await getDocs(chatsRef);

      const updatePromises: Promise<void>[] = [];

      for (const chatDoc of chatsSnapshot.docs) {
        const data = chatDoc.data();
        if (data.lastUsedModel) {
          const isValid = await isValidModel(data.lastUsedModel);
          if (!isValid) {

            updatePromises.push(
              updateDoc(doc(db, 'usuarios', username, 'conversas', chatDoc.id), {
                lastUsedModel: ''
              })
            );
          }
        }
      }

      if (updatePromises.length > 0) {
        await Promise.all(updatePromises);

      }
    } catch (error) {
      console.error('Error cleaning invalid models from chats:', error);
    }
  };

  // Função para carregar o último modelo usado globalmente (fallback)
  const loadGlobalLastUsedModel = async () => {
    if (!user) return;

    try {
      const username = await getUsernameFromFirestore();
      const userRef = doc(db, 'usuarios', username, 'configuracoes', 'settings');
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        const data = userDoc.data();
        if (data.lastUsedModel) {
          // Verificar se o modelo salvo ainda é válido
          const isValid = await isValidModel(data.lastUsedModel);
          if (isValid) {
            setSelectedModel(data.lastUsedModel);

          } else {

            // Limpar o modelo inválido das configurações
            await updateDoc(userRef, {
              lastUsedModel: null
            });
            // Carregar o modelo padrão do endpoint ativo
            loadDefaultModelFromActiveEndpoint();
          }
        } else {
          // Se não há último modelo usado, carregar o modelo padrão do endpoint ativo
          loadDefaultModelFromActiveEndpoint();
        }
      } else {
        // Se não há configurações, carregar o modelo padrão do endpoint ativo
        loadDefaultModelFromActiveEndpoint();
      }
    } catch (error) {
      console.error('Error loading global last used model:', error);
      // Fallback para carregar o modelo padrão do endpoint ativo
      loadDefaultModelFromActiveEndpoint();
    }
  };

  // Função wrapper para setSelectedModel que também salva no Firestore
  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);

    // Salvar no chat específico se houver um chat ativo
    if (actualChatId) {
      saveLastUsedModelForChat(modelId, actualChatId);
    }
  };

  // Função para criar um chat automaticamente
  const createAutoChat = async (firstMessage: string): Promise<string | null> => {
    if (!user?.email) return null;

    try {
      // Buscar username do usuário
      const usuariosRef = collection(db, 'usuarios');
      const q = query(usuariosRef, where('email', '==', user.email));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) return null;

      const userDoc = querySnapshot.docs[0];
      const userData = userDoc.data();
      const username = userData.username;

      // Gerar ID único para o chat
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 8);
      const chatId = `chat_${timestamp}_${random}`;
      const now = new Date().toISOString();

      // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)
      let finalChatName = 'Nova Conversa';
      if (firstMessage.trim().length > 0) {
        const words = firstMessage.trim().split(' ');
        const chatName = words.slice(0, Math.min(4, words.length)).join(' ');
        finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + '...' : chatName;
      }

      // Dados para o Firestore
      const firestoreData = {
        context: '',
        createdAt: now,
        folderId: null,
        frequencyPenalty: 1.0,
        isFixed: false,
        lastUpdatedAt: now,
        lastUsedModel: selectedModel,
        latexInstructions: false,
        maxTokens: 2048,
        name: finalChatName,
        password: '',
        repetitionPenalty: 1.0,
        sessionTime: {
          lastSessionStart: now,
          lastUpdated: now,
          totalTime: 0
        },
        systemPrompt: '',
        temperature: 1.0,
        ultimaMensagem: firstMessage || 'Anexo enviado',
        ultimaMensagemEm: now,
        updatedAt: now
      };

      // Criar documento no Firestore
      await setDoc(doc(db, 'usuarios', username, 'conversas', chatId), firestoreData);

      // Criar arquivo chat.json no Storage
      const chatJsonData = {
        id: chatId,
        name: finalChatName,
        messages: [],
        createdAt: now,
        lastUpdated: now
      };

      const chatJsonBlob = new Blob([JSON.stringify(chatJsonData, null, 2)], {
        type: 'application/json'
      });

      const firebaseStorageRef = storageRef(storage, `usuarios/${username}/conversas/${chatId}/chat.json`);
      await uploadBytes(firebaseStorageRef, chatJsonBlob);

      console.log('Chat criado automaticamente:', chatId);
      // Definir o nome do chat imediatamente após criação
      setChatName(finalChatName);
      return chatId;

    } catch (error) {
      console.error('Erro ao criar chat automaticamente:', error);
      return null;
    }
  };

  const handleSendMessage = async (attachments?: import('@/lib/types/chat').AttachmentMetadata[], webSearchEnabled?: boolean) => {

    // Obter anexos históricos ativos
    const historicalAttachments = getAllChatAttachments().filter(att => att.isActive !== false);

    // Combinar anexos novos com anexos históricos ativos
    const allAttachmentsToSend = [
      ...(attachments || []), // Anexos novos (sempre incluídos)
      ...historicalAttachments // Anexos históricos ativos
    ];

    // Remover duplicatas baseado no ID
    const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self) =>
      index === self.findIndex(a => a.id === attachment.id)
    );

    if ((!message.trim() && (!attachments || attachments.length === 0)) || isLoading || isStreaming) {
      return;
    }
    if (!user?.email) return;

    const userMessage: Message = {
      id: aiService.generateMessageId(),
      content: message.trim(),
      sender: 'user',
      timestamp: new Date().toISOString(),
      attachments: attachments || [], // Salvar apenas anexos novos na mensagem
    };

    // Se não há chat atual, criar um automaticamente
    let chatIdToUse = actualChatId;
    if (!chatIdToUse) {
      const messageForChat = message.trim() || (attachments && attachments.length > 0 ? 'Anexo enviado' : 'Nova conversa');
      chatIdToUse = await createAutoChat(messageForChat);
      if (chatIdToUse) {
        setActualChatId(chatIdToUse);
        // O nome já foi definido na função createAutoChat, mas vamos garantir carregando também
        loadChatName(chatIdToUse);
        onChatCreated?.(chatIdToUse);
      }
    }

    if (!chatIdToUse) {
      console.error('Não foi possível criar ou obter chat ID');
      return;
    }

    // Adicionar mensagem do usuário
    setMessages(prev => [...prev, userMessage]);
    const currentMessage = message.trim() || ""; // Permitir mensagem vazia se houver anexos
    setMessage('');
    setIsLoading(true);
    setIsStreaming(true);

    // Mover chat para o topo com animação
    if (chatIdToUse && sidebarRef?.current) {
      sidebarRef.current.moveToTop(chatIdToUse);
    }

    // Preparar ID para a mensagem da IA que será criada durante o streaming
    const aiMessageId = aiService.generateMessageId();
    setStreamingMessageId(aiMessageId);

    // Buscar username correto do usuário
    const username = await getUsernameFromFirestore();

    // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)
    await aiService.sendMessageSafe(
      {
        username: username,
        chatId: chatIdToUse,
        message: currentMessage,
        model: selectedModel,
        attachments: uniqueAttachments,
        webSearchEnabled: webSearchEnabled,
        userMessageId: userMessage.id, // Passar o ID da mensagem do usuário
      },
      // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk
      (chunk: string) => {
        setMessages(prev => {
          // Verificar se a mensagem da IA já existe
          const existingMessageIndex = prev.findIndex(msg => msg.id === aiMessageId);

          if (existingMessageIndex !== -1) {
            // Atualizar mensagem existente
            return prev.map(msg =>
              msg.id === aiMessageId
                ? { ...msg, content: msg.content + chunk }
                : msg
            );
          } else {
            // Criar nova mensagem da IA na primeira chunk
            // Remover o indicador de loading assim que a primeira chunk chegar
            setIsLoading(false);

            const aiMessage: Message = {
              id: aiMessageId,
              content: chunk,
              sender: 'ai',
              timestamp: new Date().toISOString(),
              hasWebSearch: webSearchEnabled,
            };
            return [...prev, aiMessage];
          }
        });
      },
      // onComplete - finalizar streaming
      (fullResponse: string) => {
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessageId
            ? { ...msg, content: fullResponse }
            : msg
        ));
        setIsLoading(false);
        setIsStreaming(false);
        setStreamingMessageId(null);

        // Salvar o modelo usado no chat específico
        if (chatIdToUse) {
          saveLastUsedModelForChat(selectedModel, chatIdToUse);
        }

        // Atualizar saldo do OpenRouter após a resposta com delay de 5 segundos
        if (onUpdateOpenRouterBalance) {
          setTimeout(() => {
            onUpdateOpenRouterBalance();
          }, 5000);
        }
      },
      // onError - tratar erros
      (error: string) => {
        console.error('Erro na IA:', error);
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessageId
            ? { ...msg, content: `❌ Erro: ${error}` }
            : msg
        ));
        setIsLoading(false);
        setIsStreaming(false);
        setStreamingMessageId(null);
      }
    );
  };

  // Função para cancelar streaming
  const handleCancelStreaming = () => {
    aiService.cancelRequest();
    setIsLoading(false);
    setIsStreaming(false);
    setStreamingMessageId(null);
  };

  // Função para carregar o nome do chat do Firestore
  const loadChatName = async (chatId: string) => {
    if (!user?.email) return;

    try {
      const username = await getUsernameFromFirestore();
      const chatDoc = await getDoc(doc(db, 'usuarios', username, 'conversas', chatId));

      if (chatDoc.exists()) {
        const chatData = chatDoc.data();
        const chatName = chatData.name || 'Conversa sem nome';
        setChatName(chatName);
      } else {
        setChatName('Conversa não encontrada');
      }
    } catch (error) {
      console.error('Erro ao carregar nome do chat:', error);
      setChatName('Erro ao carregar nome');
    }
  };

  // Função para carregar mensagens existentes do chat
  const loadChatMessages = async (chatId: string) => {
    if (!user?.email) return;

    setIsLoadingChat(true);
    try {
      const username = await getUsernameFromFirestore();
      const chatMessages = await aiService.loadChatMessages(username, chatId);
      const convertedMessages = aiService.convertFromAIFormat(chatMessages);
      setMessages(convertedMessages);
    } catch (error) {
      console.error('❌ Erro ao carregar mensagens do chat:', error);
      setMessages([]);
    } finally {
      setIsLoadingChat(false);
    }
  };

  // Carregar último modelo usado globalmente quando o componente montar (apenas se não há chat)
  useEffect(() => {
    if (user && !currentChat) {
      // Limpar modelos inválidos uma vez quando o usuário faz login
      cleanupInvalidModelsFromAllChats();
      loadGlobalLastUsedModel();
    }
  }, [user, currentChat]);

  // Carregar mensagens quando o chat atual mudar
  useEffect(() => {
    if (currentChat && currentChat !== actualChatId) {
      setActualChatId(currentChat);
      setIsLoadingChat(true);
      // Limpar mensagens imediatamente para mostrar o estado de carregamento
      setMessages([]);
      loadChatMessages(currentChat);
      loadChatName(currentChat);
      // Carregar o modelo específico do chat
      loadLastUsedModelForChat(currentChat);
    } else if (!currentChat && actualChatId) {
      // Só resetar se realmente não há chat e havia um chat antes
      setActualChatId(null);
      setMessages([]);
      setChatName('Nova Conversa');
      setIsLoadingChat(false);
      // Carregar modelo global quando não há chat específico
      loadGlobalLastUsedModel();
    }
  }, [currentChat, user?.email]);

  // Funções para manipular mensagens
  const handleDeleteMessage = async (messageId: string) => {
    if (!actualChatId || !user?.email) return;

    // Remover visualmente primeiro para melhor UX
    setMessages(prev => prev.filter(msg => msg.id !== messageId));

    try {
      const username = await getUsernameFromFirestore();
      const success = await aiService.deleteMessage(username, actualChatId, messageId);

      if (!success) {
        // Se falhou, restaurar a mensagem
        loadChatMessages(actualChatId);
        console.error('Falha ao deletar mensagem no servidor');
      }
    } catch (error) {
      // Se falhou, restaurar a mensagem
      loadChatMessages(actualChatId);
      console.error('Erro ao deletar mensagem:', error);
    }
  };

  const handleRegenerateMessage = async (messageId: string) => {
    if (!actualChatId || !user?.email) return;

    // ✅ CORREÇÃO: Recarregar mensagens do Firebase Storage para garantir estado atualizado
    console.log('🔄 Recarregando mensagens antes da regeneração para garantir estado atualizado...');

    try {
      const username = await getUsernameFromFirestore();

      // Carregar mensagens diretamente do Firebase Storage
      const freshMessages = await aiService.loadChatMessages(username, actualChatId);
      const convertedFreshMessages = aiService.convertFromAIFormat(freshMessages);

      console.log('📥 Mensagens recarregadas do Storage:', convertedFreshMessages.length);

      // Buscar o índice da mensagem nas mensagens frescas
      const messageIndex = convertedFreshMessages.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) {
        console.error('❌ Mensagem não encontrada após recarregar:', messageId);
        setIsLoading(false);
        setIsStreaming(false);
        return;
      }

      const messageToRegenerate = convertedFreshMessages[messageIndex];
      console.log('📝 Mensagem que será regenerada:', {
        id: messageToRegenerate.id,
        content: messageToRegenerate.content.substring(0, 100) + '...',
        index: messageIndex
      });

      // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)
      const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);
      setMessages(messagesBeforeRegeneration);

      // Preparar o conteúdo da mensagem para regenerar
      setMessage(messageToRegenerate.content);
      setIsLoading(true);
      setIsStreaming(true);

      // Preparar ID para a nova mensagem da IA que será criada durante o streaming
      const aiMessageId = aiService.generateMessageId();
      setStreamingMessageId(aiMessageId);

      // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)
      console.log(`🗑️ Deletando ${convertedFreshMessages.length - messageIndex - 1} mensagens posteriores...`);
      for (let i = messageIndex + 1; i < convertedFreshMessages.length; i++) {
        const msgToDelete = convertedFreshMessages[i];
        console.log('🗑️ Deletando mensagem:', msgToDelete.id);
        await aiService.deleteMessage(username, actualChatId, msgToDelete.id);
      }

      // Enviar para a IA para regenerar
      await aiService.sendMessageSafe(
        {
          username: username,
          chatId: actualChatId,
          message: messageToRegenerate.content,
          model: selectedModel,
          isRegeneration: true,
          userMessageId: messageToRegenerate.id, // Passar o ID da mensagem que está sendo regenerada
        },
        // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk
        (chunk: string) => {
          setMessages(prev => {
            // Verificar se a mensagem da IA já existe
            const existingMessageIndex = prev.findIndex(msg => msg.id === aiMessageId);

            if (existingMessageIndex !== -1) {
              // Atualizar mensagem existente
              return prev.map(msg =>
                msg.id === aiMessageId
                  ? { ...msg, content: msg.content + chunk }
                  : msg
              );
            } else {
              // Criar nova mensagem da IA na primeira chunk
              // Remover o indicador de loading assim que a primeira chunk chegar
              setIsLoading(false);

              const aiMessage: Message = {
                id: aiMessageId,
                content: chunk,
                sender: 'ai',
                timestamp: new Date().toISOString(),
                hasWebSearch: false, // Regeneração não usa web search por padrão
              };
              return [...prev, aiMessage];
            }
          });
        },
        // onComplete - finalizar streaming
        (fullResponse: string) => {
          setMessages(prev => prev.map(msg =>
            msg.id === aiMessageId
              ? { ...msg, content: fullResponse }
              : msg
          ));
          setIsLoading(false);
          setIsStreaming(false);
          setStreamingMessageId(null);
          setMessage(''); // Limpar o campo de input

          // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos
          if (onUpdateOpenRouterBalance) {
            setTimeout(() => {
              onUpdateOpenRouterBalance();
            }, 5000);
          }
        },
        // onError - tratar erros
        (error: string) => {
          console.error('Erro na regeneração:', error);
          setMessages(prev => prev.map(msg =>
            msg.id === aiMessageId
              ? { ...msg, content: `❌ Erro na regeneração: ${error}` }
              : msg
          ));
          setIsLoading(false);
          setIsStreaming(false);
          setStreamingMessageId(null);
          setMessage(''); // Limpar o campo de input
        }
      );

    } catch (error) {
      console.error('❌ Erro ao regenerar mensagem:', error);
      setIsLoading(false);
      setIsStreaming(false);
      setStreamingMessageId(null);
      setMessage(''); // Limpar o campo de input

      // Recarregar mensagens em caso de erro
      loadChatMessages(actualChatId);
    }
  };



  const handleEditMessage = async (messageId: string, newContent: string): Promise<boolean> => {
    if (!actualChatId || !user?.email) return false;

    console.log('✏️ Iniciando edição de mensagem:', {
      messageId,
      chatId: actualChatId,
      newContentLength: newContent.length,
      newContentPreview: newContent.substring(0, 100) + '...'
    });

    // Atualizar visualmente primeiro para melhor UX
    setMessages(prev => prev.map(msg =>
      msg.id === messageId ? { ...msg, content: newContent } : msg
    ));

    try {
      const username = await getUsernameFromFirestore();
      console.log('📤 Enviando atualização para o servidor...');

      const success = await aiService.updateMessage(username, actualChatId, messageId, newContent);

      if (!success) {
        // Se falhou, restaurar o conteúdo original
        console.error('❌ Falha ao atualizar mensagem no servidor');
        loadChatMessages(actualChatId);
        return false;
      } else {
        console.log('✅ Mensagem editada e salva com sucesso no Firebase Storage:', {
          messageId,
          timestamp: new Date().toISOString()
        });
        return true;
      }
    } catch (error) {
      // Se falhou, restaurar o conteúdo original
      console.error('❌ Erro ao atualizar mensagem:', error);
      loadChatMessages(actualChatId);
      return false;
    }
  };

  const handleEditAndRegenerate = async (messageId: string, newContent: string): Promise<void> => {
    if (!actualChatId || !user?.email) return;

    console.log('✏️🔄 Iniciando edição e regeneração de mensagem:', {
      messageId,
      chatId: actualChatId,
      newContentLength: newContent.length,
      newContentPreview: newContent.substring(0, 100) + '...'
    });

    try {
      // 1. Primeiro, salvar a edição
      const editSuccess = await handleEditMessage(messageId, newContent);
      if (!editSuccess) {
        console.error('❌ Falha ao editar mensagem, cancelando regeneração');
        return;
      }

      console.log('✅ Mensagem editada com sucesso, iniciando regeneração...');

      // 2. Aguardar um pouco para garantir que a edição foi salva
      await new Promise(resolve => setTimeout(resolve, 200));

      // 3. Recarregar mensagens do Firebase Storage para garantir estado atualizado
      console.log('🔄 Recarregando mensagens antes da regeneração...');

      const username = await getUsernameFromFirestore();

      // Carregar mensagens diretamente do Firebase Storage
      const freshMessages = await aiService.loadChatMessages(username, actualChatId);
      const convertedFreshMessages = aiService.convertFromAIFormat(freshMessages);

      console.log('📥 Mensagens recarregadas do Storage:', convertedFreshMessages.length);

      // 4. Buscar o índice da mensagem nas mensagens frescas
      const messageIndex = convertedFreshMessages.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) {
        console.error('❌ Mensagem não encontrada após recarregar:', messageId);
        return;
      }

      const messageToRegenerate = convertedFreshMessages[messageIndex];
      console.log('📝 Mensagem que será regenerada:', {
        id: messageToRegenerate.id,
        content: messageToRegenerate.content.substring(0, 100) + '...',
        index: messageIndex
      });

      // 5. Verificar se há mensagens após esta mensagem
      const hasMessagesAfter = messageIndex < convertedFreshMessages.length - 1;
      console.log(`📊 Mensagens após esta: ${convertedFreshMessages.length - messageIndex - 1}`);

      // 6. Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)
      const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);
      setMessages(messagesBeforeRegeneration);

      // 7. Preparar o conteúdo da mensagem para regenerar
      setMessage(messageToRegenerate.content);
      setIsLoading(true);
      setIsStreaming(true);

      // 8. Preparar ID para a nova mensagem da IA que será criada durante o streaming
      const aiMessageId = aiService.generateMessageId();
      setStreamingMessageId(aiMessageId);

      // 9. Deletar apenas as mensagens POSTERIORES do Firebase Storage (se houver)
      if (hasMessagesAfter) {
        console.log(`🗑️ Deletando ${convertedFreshMessages.length - messageIndex - 1} mensagens posteriores...`);
        for (let i = messageIndex + 1; i < convertedFreshMessages.length; i++) {
          const msgToDelete = convertedFreshMessages[i];
          console.log('🗑️ Deletando mensagem:', msgToDelete.id);
          await aiService.deleteMessage(username, actualChatId, msgToDelete.id);
        }
      }

      // 10. Enviar para a IA para regenerar
      await aiService.sendMessageSafe(
        {
          username: username,
          chatId: actualChatId,
          message: messageToRegenerate.content,
          model: selectedModel,
          isRegeneration: true,
          userMessageId: messageToRegenerate.id, // Passar o ID da mensagem que está sendo regenerada
        },
        // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk
        (chunk: string) => {
          setMessages(prev => {
            // Verificar se a mensagem da IA já existe
            const existingMessageIndex = prev.findIndex(msg => msg.id === aiMessageId);

            if (existingMessageIndex !== -1) {
              // Atualizar mensagem existente
              return prev.map(msg =>
                msg.id === aiMessageId
                  ? { ...msg, content: msg.content + chunk }
                  : msg
              );
            } else {
              // Criar nova mensagem da IA na primeira chunk
              // Remover o indicador de loading assim que a primeira chunk chegar
              setIsLoading(false);

              const aiMessage: Message = {
                id: aiMessageId,
                content: chunk,
                sender: 'ai',
                timestamp: new Date().toISOString(),
                hasWebSearch: false, // Regeneração não usa web search por padrão
              };
              return [...prev, aiMessage];
            }
          });
        },
        // onComplete - finalizar streaming
        (fullResponse: string) => {
          setMessages(prev => prev.map(msg =>
            msg.id === aiMessageId
              ? { ...msg, content: fullResponse }
              : msg
          ));
          setIsLoading(false);
          setIsStreaming(false);
          setStreamingMessageId(null);
          setMessage(''); // Limpar o campo de input

          // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos
          if (onUpdateOpenRouterBalance) {
            setTimeout(() => {
              onUpdateOpenRouterBalance();
            }, 5000);
          }
        },
        // onError - tratar erros
        (error: string) => {
          console.error('Erro na regeneração:', error);
          setMessages(prev => prev.map(msg =>
            msg.id === aiMessageId
              ? { ...msg, content: `❌ Erro na regeneração: ${error}` }
              : msg
          ));
          setIsLoading(false);
          setIsStreaming(false);
          setStreamingMessageId(null);
          setMessage(''); // Limpar o campo de input
        }
      );

    } catch (error) {
      console.error('❌ Erro ao editar e regenerar mensagem:', error);
      setIsLoading(false);
      setIsStreaming(false);
      setStreamingMessageId(null);
      setMessage(''); // Limpar o campo de input

      // Recarregar mensagens em caso de erro
      loadChatMessages(actualChatId);
    }
  };

  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content).then(() => {
      console.log('Mensagem copiada para a área de transferência');
    });
  };

  // Funções de navegação
  const handleScrollToTop = () => {
    const scrollContainer = chatInterfaceRef.current?.querySelector('.overflow-y-auto');
    if (scrollContainer) {
      scrollContainer.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const handleScrollToBottom = () => {
    const scrollContainer = chatInterfaceRef.current?.querySelector('.overflow-y-auto');
    if (scrollContainer) {
      scrollContainer.scrollTo({
        top: scrollContainer.scrollHeight,
        behavior: 'smooth'
      });
    }
  };



  // Função para converter mensagens para o formato ChatMessage
  const convertToChatMessages = (messages: Message[]): ChatMessage[] => {
    return messages.map(msg => ({
      id: msg.id,
      content: msg.content,
      role: msg.sender === 'user' ? 'user' : 'assistant',
      timestamp: new Date(msg.timestamp).getTime(),
      isFavorite: msg.isFavorite || false,
      attachments: msg.attachments || []
    }));
  };

  // Função para abrir o modal de download
  const handleDownloadModal = (chatId?: string) => {
    // Se um chatId específico foi fornecido e é diferente do atual, muda para esse chat primeiro
    if (chatId && chatId !== currentChat && onChatSelect) {
      onChatSelect(chatId);
      // Abre o modal após um pequeno delay para garantir que o chat foi carregado
      setTimeout(() => {
        setIsDownloadModalOpen(true);
      }, 100);
    } else {
      setIsDownloadModalOpen(true);
    }
  };

  // Função para abrir o modal de anexos
  const handleAttachmentsModal = (chatId?: string) => {
    // Se um chatId específico foi fornecido e é diferente do atual, muda para esse chat primeiro
    if (chatId && chatId !== currentChat && onChatSelect) {
      onChatSelect(chatId);
      setTimeout(() => {
        setIsAttachmentsModalOpen(true);
      }, 100);
    } else {
      setIsAttachmentsModalOpen(true);
    }
  };

  // Função para abrir o modal de estatísticas
  const handleStatisticsModal = (chatId?: string) => {
    // Se um chatId específico foi fornecido e é diferente do atual, muda para esse chat primeiro
    if (chatId && chatId !== currentChat && onChatSelect) {
      onChatSelect(chatId);
      setTimeout(() => {
        setIsStatisticsModalOpen(true);
      }, 100);
    } else {
      setIsStatisticsModalOpen(true);
    }
  };

  // Expor as funções para o componente pai
  useImperativeHandle(ref, () => ({
    handleDownloadModal,
    handleAttachmentsModal,
    handleStatisticsModal
  }));

  // Função para obter todos os anexos do chat
  const getAllChatAttachments = (): import('@/lib/types/chat').AttachmentMetadata[] => {
    const allAttachments: import('@/lib/types/chat').AttachmentMetadata[] = [];

    messages.forEach(message => {
      if (message.attachments && message.attachments.length > 0) {
        allAttachments.push(...message.attachments);
      }
    });

    // Remover duplicatas baseado no ID
    const uniqueAttachments = allAttachments.filter((attachment, index, self) =>
      index === self.findIndex(a => a.id === attachment.id)
    );

    return uniqueAttachments;
  };

  // Função para salvar o estado dos anexos no Firebase Storage
  const saveAttachmentStates = async (updatedMessages: Message[]) => {
    if (!currentUsername || !actualChatId) return;

    try {
      // Preparar dados do chat para salvar
      const chatData = {
        id: actualChatId,
        name: chatName || 'Chat',
        messages: updatedMessages.map(msg => ({
          id: msg.id,
          content: msg.content,
          role: msg.sender === 'user' ? 'user' : 'assistant',
          timestamp: msg.timestamp,
          isFavorite: msg.isFavorite,
          attachments: msg.attachments
        })),
        createdAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString()
      };

      // Salvar no Firebase Storage
      const chatJsonBlob = new Blob([JSON.stringify(chatData, null, 2)], {
        type: 'application/json'
      });

      const chatJsonRef = storageRef(storage, `usuarios/${currentUsername}/conversas/${actualChatId}/chat.json`);
      await uploadBytes(chatJsonRef, chatJsonBlob);

      console.log('✅ Estado dos anexos salvo no Firebase Storage');
      console.log('📁 Dados salvos:', {
        chatId: actualChatId,
        totalMessages: chatData.messages.length,
        messagesWithAttachments: chatData.messages.filter(msg => msg.attachments && msg.attachments.length > 0).length
      });
    } catch (error) {
      console.error('❌ Erro ao salvar estado dos anexos:', error);
    }
  };

  // Função para alternar o estado ativo de um anexo
  const handleToggleAttachment = (attachmentId: string) => {
    setMessages(prevMessages => {
      const updatedMessages = prevMessages.map(message => {
        if (message.attachments && message.attachments.length > 0) {
          const updatedAttachments = message.attachments.map(attachment => {
            if (attachment.id === attachmentId) {
              // Se isActive não está definido, considerar como true (ativo por padrão)
              const currentState = attachment.isActive !== false;
              return { ...attachment, isActive: !currentState };
            }
            return attachment;
          });
          return { ...message, attachments: updatedAttachments };
        }
        return message;
      });

      // Salvar o estado atualizado no Firebase Storage
      saveAttachmentStates(updatedMessages);

      return updatedMessages;
    });
  };

  // Função para filtrar anexos ativos para envio à IA
  const getActiveAttachments = (attachments?: import('@/lib/types/chat').AttachmentMetadata[]): import('@/lib/types/chat').AttachmentMetadata[] => {
    if (!attachments) return [];

    // Para anexos novos (sem isActive definido), incluir por padrão
    // Para anexos existentes, verificar se isActive não é false
    return attachments.filter(attachment => {
      // Se isActive não está definido (anexo novo), incluir
      if (attachment.isActive === undefined) return true;
      // Se isActive está definido, incluir apenas se não for false
      return attachment.isActive !== false;
    });
  };



  // Função para obter IDs dos anexos ativos
  const getActiveAttachmentIds = (): string[] => {
    const allAttachments = getAllChatAttachments();
    return allAttachments.filter(att => att.isActive !== false).map(att => att.id);
  };

  // Funções para drag-n-drop
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => prev + 1);

    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => prev - 1);

    // Só remove o overlay quando o contador chega a 0
    // Isso evita flickering quando o drag passa por elementos filhos
    if (dragCounter <= 1) {
      setIsDragOver(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Permitir drop apenas se há arquivos sendo arrastados
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      e.dataTransfer.dropEffect = 'copy';
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsDragOver(false);
    setDragCounter(0);

    const files = Array.from(e.dataTransfer.files);

    if (files.length === 0) return;

    // Verificar se temos username necessário
    if (!currentUsername) {
      console.error('Username não disponível para upload de anexos');
      return;
    }

    // Se não há chat atual, criar um automaticamente para poder fazer upload dos anexos
    let chatIdToUse = actualChatId;
    if (!chatIdToUse) {
      const messageForChat = files.length === 1
        ? `Arquivo anexado: ${files[0].name}`
        : `${files.length} arquivos anexados`;
      chatIdToUse = await createAutoChat(messageForChat);
      if (chatIdToUse) {
        setActualChatId(chatIdToUse);
        loadChatName(chatIdToUse);
        onChatCreated?.(chatIdToUse);
      }
    }

    if (!chatIdToUse) {
      console.error('Não foi possível criar ou obter chat ID para anexos');
      return;
    }

    try {
      // Importar o attachmentService dinamicamente
      const { default: attachmentService } = await import('@/lib/services/attachmentService');

      // Fazer upload dos arquivos
      const uploadedAttachments = await attachmentService.uploadMultipleAttachments(
        files,
        currentUsername,
        chatIdToUse
      );

      // Em vez de enviar a mensagem, vamos notificar o InputBar sobre os novos anexos
      // Isso será feito através de um evento customizado
      const attachmentMetadata = uploadedAttachments.map(att => att.metadata);

      // Disparar evento customizado para o InputBar capturar
      const event = new CustomEvent('dragDropAttachments', {
        detail: {
          attachments: attachmentMetadata,
          chatId: chatIdToUse,
          username: currentUsername
        }
      });
      window.dispatchEvent(event);

      console.log(`✅ ${files.length} arquivo(s) adicionado(s) como anexo via drag-n-drop`);

    } catch (error) {
      console.error('❌ Erro ao processar arquivos via drag-n-drop:', error);
    }
  };

  return (
    <div
      className="flex-1 flex flex-col h-screen relative"
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {/* Drag-n-Drop Overlay */}
      {isDragOver && (
        <div className="absolute inset-0 z-50 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center">
          <div className="bg-blue-800/90 backdrop-blur-md rounded-2xl p-8 border-2 border-dashed border-blue-400 shadow-2xl">
            <div className="text-center">
              <div className="mb-4">
                <svg className="w-16 h-16 text-blue-300 mx-auto animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-blue-100 mb-2">
                Solte os arquivos aqui
              </h3>
              <p className="text-blue-300 text-sm">
                Os arquivos serão adicionados como anexos à conversa
              </p>
            </div>
          </div>
        </div>
      )}



      {/* ChatInterface */}
      <div ref={chatInterfaceRef} className="flex-1 min-h-0" style={{ height: 'calc(100vh - 140px)' }}>
        <ChatInterface
          chatId={actualChatId || 'temp-chat'}
          messages={messages}
          isLoading={isLoading}
          isLoadingChat={isLoadingChat}
          isStreaming={isStreaming}
          streamingMessageId={streamingMessageId || undefined}
          enableSessions={appearanceSettings.sessionsEnabled}
          onDeleteMessage={handleDeleteMessage}
          onRegenerateMessage={handleRegenerateMessage}
          onEditMessage={handleEditMessage}
          onEditAndRegenerate={handleEditAndRegenerate}
          onCopyMessage={handleCopyMessage}
        />
      </div>

      {/* InputBar */}
      <InputBar
        message={message}
        setMessage={setMessage}
        onSendMessage={handleSendMessage}
        isLoading={isLoading}
        selectedModel={selectedModel}
        onModelChange={handleModelChange}
        onScrollToTop={handleScrollToTop}
        onScrollToBottom={handleScrollToBottom}
        isStreaming={isStreaming}
        onCancelStreaming={handleCancelStreaming}
        onOpenModelModal={() => setIsModelModalOpen(true)}
        onOpenAttachmentsModal={handleAttachmentsModal}
        username={currentUsername}
        chatId={actualChatId || undefined}
        activeAttachmentsCount={getActiveAttachmentIds().length}
      />

      {/* Download Modal */}
      <DownloadModal
        isOpen={isDownloadModalOpen}
        onClose={() => setIsDownloadModalOpen(false)}
        messages={convertToChatMessages(messages)}
        chatName={chatName}
      />

      {/* Model Selection Modal */}
      <ModelSelectionModal
        isOpen={isModelModalOpen}
        onClose={() => setIsModelModalOpen(false)}
        currentModel={selectedModel}
        onModelSelect={handleModelChange}
      />

      {/* Attachments Modal */}
      <AttachmentsModal
        isOpen={isAttachmentsModalOpen}
        onClose={() => setIsAttachmentsModalOpen(false)}
        attachments={getAllChatAttachments()}
        activeAttachments={getActiveAttachmentIds()}
        onToggleAttachment={handleToggleAttachment}
      />

      {/* Statistics Modal */}
      <StatisticsModal
        isOpen={isStatisticsModalOpen}
        onClose={() => setIsStatisticsModalOpen(false)}
        messages={convertToChatMessages(messages)}
        chatName={chatName}
      />
    </div>
  );
});

ChatArea.displayName = 'ChatArea';

export default ChatArea;
