"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n/* harmony import */ var _chatSessionsService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chatSessionsService */ \"(app-pages-browser)/./src/lib/services/chatSessionsService.ts\");\n// Serviço para integração com Firebase Functions de IA\n\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model,\n                attachments: config.attachments || [],\n                isRegeneration: config.isRegeneration || false,\n                webSearchEnabled: config.webSearchEnabled || false,\n                userMessageId: config.userMessageId\n            };\n            console.log(\"=== DEBUG: AI SERVICE REQUEST DATA ===\");\n            console.log(\"Request data:\", JSON.stringify(requestData, null, 2));\n            console.log(\"Attachments length:\", requestData.attachments.length);\n            if (requestData.attachments.length > 0) {\n                console.log(\"First attachment:\", JSON.stringify(requestData.attachments[0], null, 2));\n            }\n            console.log(\"\\uD83D\\uDD0D Username:\", requestData.username);\n            console.log(\"\\uD83D\\uDD0D ChatId:\", requestData.chatId);\n            console.log(\"\\uD83D\\uDD0D Message:\", requestData.message);\n            // Enviar requisição\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            // Processar stream\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                // Debug: Verificar se a resposta contém instruções de LaTeX\n                console.log(\"\\uD83D\\uDD0D DEBUG: Resposta completa do backend recebida\");\n                console.log(\"\\uD83D\\uDD0D Tamanho da resposta:\", fullResponse.length);\n                console.log('\\uD83D\\uDD0D Cont\\xe9m \"LaTeX\":', fullResponse.toLowerCase().includes(\"latex\"));\n                console.log('\\uD83D\\uDD0D Cont\\xe9m \"INSTRU\\xc7\\xd5ES\":', fullResponse.toLowerCase().includes(\"instru\\xe7\\xf5es\"));\n                console.log('\\uD83D\\uDD0D Cont\\xe9m \"REGRAS FUNDAMENTAIS\":', fullResponse.toLowerCase().includes(\"regras fundamentais\"));\n                console.log(\"\\uD83D\\uDD0D Primeiros 300 caracteres:\", fullResponse.substring(0, 300));\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    return;\n                }\n                onError(error.message);\n            } else {\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtém contexto completo do chat para envio à IA\n   * IMPORTANTE: Sempre usa TODAS as mensagens, independente das sessões\n   */ async getFullChatContextForAI(chatId) {\n        try {\n            // Tenta obter contexto do sistema de sessões primeiro\n            const context = await _chatSessionsService__WEBPACK_IMPORTED_MODULE_0__.chatSessionsService.getFullChatContext(chatId);\n            if (context && context.allMessages.length > 0) {\n                // Converte mensagens do formato interno para formato da IA\n                return this.convertToAIFormat(context.allMessages);\n            }\n            // Fallback: se não há sessões, retorna array vazio\n            // O contexto será construído normalmente pelo carregamento direto\n            return [];\n        } catch (error) {\n            console.warn(\"Sistema de sess\\xf5es n\\xe3o dispon\\xedvel, usando carregamento direto:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n        if (config.message.length > 10000) {\n            throw new Error(\"Mensagem muito longa (m\\xe1ximo 10.000 caracteres)\");\n        }\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        try {\n            this.validateConfig(config);\n            await this.sendMessage(config, onChunk, onComplete, onError);\n        } catch (error) {\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});