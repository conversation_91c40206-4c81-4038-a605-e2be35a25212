"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/contexts/AppearanceContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/AppearanceContext.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppearanceProvider: function() { return /* binding */ AppearanceProvider; },\n/* harmony export */   useAppearance: function() { return /* binding */ useAppearance; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useAppearance,AppearanceProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Configurações padrão\nconst DEFAULT_SETTINGS = {\n    fonte: \"Inter\",\n    tamanhoFonte: 14,\n    palavrasPorSessao: 5000,\n    sessionsEnabled: true,\n    latexAutomatico: false\n};\n// Criar contexto\nconst AppearanceContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Hook para usar o contexto\nconst useAppearance = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppearanceContext);\n    if (!context) {\n        throw new Error(\"useAppearance must be used within an AppearanceProvider\");\n    }\n    return context;\n};\n_s(useAppearance, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Função para obter username do Firestore\nconst getUsernameFromFirestore = async (userEmail)=>{\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", userEmail);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const userData = userDoc.data();\n            return userData.username || userEmail;\n        }\n        return userEmail;\n    } catch (error) {\n        console.error(\"Erro ao obter username:\", error);\n        return userEmail;\n    }\n};\n// Provider do contexto\nconst AppearanceProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_SETTINGS);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Carregar configurações do Firestore\n    const loadSettings = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) {\n            setSettings(DEFAULT_SETTINGS);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            setIsLoading(true);\n            const username = await getUsernameFromFirestore(user.email);\n            const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(configRef);\n            if (configDoc.exists()) {\n                const config = configDoc.data();\n                if (config.aparencia) {\n                    setSettings({\n                        fonte: config.aparencia.fonte || DEFAULT_SETTINGS.fonte,\n                        tamanhoFonte: config.aparencia.tamanhoFonte || DEFAULT_SETTINGS.tamanhoFonte,\n                        palavrasPorSessao: config.aparencia.palavrasPorSessao || DEFAULT_SETTINGS.palavrasPorSessao,\n                        sessionsEnabled: config.aparencia.sessionsEnabled !== undefined ? config.aparencia.sessionsEnabled : DEFAULT_SETTINGS.sessionsEnabled,\n                        latexAutomatico: config.aparencia.latexAutomatico !== undefined ? config.aparencia.latexAutomatico : DEFAULT_SETTINGS.latexAutomatico\n                    });\n                } else {\n                    setSettings(DEFAULT_SETTINGS);\n                }\n            } else {\n                setSettings(DEFAULT_SETTINGS);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            setSettings(DEFAULT_SETTINGS);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Carregar configurações quando o usuário mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n    }, [\n        user\n    ]);\n    // Aplicar configurações às variáveis CSS globais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading) {\n            const root = document.documentElement;\n            root.style.setProperty(\"--chat-font-family\", settings.fonte);\n            root.style.setProperty(\"--chat-font-size\", \"\".concat(settings.tamanhoFonte, \"px\"));\n            root.style.setProperty(\"--chat-words-per-session\", settings.palavrasPorSessao.toString());\n            root.style.setProperty(\"--chat-sessions-enabled\", settings.sessionsEnabled ? \"1\" : \"0\");\n            root.style.setProperty(\"--chat-latex-automatico\", settings.latexAutomatico ? \"1\" : \"0\");\n        }\n    }, [\n        settings,\n        isLoading\n    ]);\n    // Função para atualizar configurações\n    const updateSettings = async (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        setSettings(updatedSettings);\n        // Salvar no Firestore automaticamente\n        if (user) {\n            try {\n                const username = await getUsernameFromFirestore(user.email);\n                const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n                // Buscar configurações existentes\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(configRef);\n                const existingConfig = configDoc.exists() ? configDoc.data() : {};\n                // Atualizar apenas a seção de aparência\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(configRef, {\n                    ...existingConfig,\n                    aparencia: updatedSettings,\n                    updatedAt: new Date().toISOString()\n                }, {\n                    merge: true\n                });\n                console.log(\"✅ Configura\\xe7\\xf5es de apar\\xeancia salvas automaticamente\");\n                console.log(\"\\uD83D\\uDCCB Configura\\xe7\\xf5es salvas:\", updatedSettings);\n            } catch (error) {\n                console.error(\"❌ Erro ao salvar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            }\n        }\n    };\n    // Função para aplicar configurações a um elemento específico\n    const applyToElement = (element)=>{\n        element.style.fontFamily = settings.fonte;\n        element.style.fontSize = \"\".concat(settings.tamanhoFonte, \"px\");\n    };\n    // Função para obter variáveis CSS como objeto\n    const getCSSVariables = ()=>{\n        return {\n            \"--chat-font-family\": settings.fonte,\n            \"--chat-font-size\": \"\".concat(settings.tamanhoFonte, \"px\"),\n            \"--chat-words-per-session\": settings.palavrasPorSessao.toString()\n        };\n    };\n    const value = {\n        settings,\n        updateSettings,\n        isLoading,\n        applyToElement,\n        getCSSVariables\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppearanceContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\contexts\\\\AppearanceContext.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AppearanceProvider, \"GTB8At3kv228hKHqctGDrslQ74E=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = AppearanceProvider;\nvar _c;\n$RefreshReg$(_c, \"AppearanceProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AppearanceContext.tsx\n"));

/***/ })

});