'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from './AuthContext';

// Interface para configurações de aparência
export interface AppearanceSettings {
  fonte: string;
  tamanhoFonte: number;
  palavrasPorSessao: number;
  sessionsEnabled: boolean;
  latexAutomatico: boolean;
}

// Interface do contexto
interface AppearanceContextType {
  settings: AppearanceSettings;
  updateSettings: (newSettings: Partial<AppearanceSettings>) => void;
  isLoading: boolean;
  applyToElement: (element: HTMLElement) => void;
  getCSSVariables: () => Record<string, string>;
}

// Configurações padrão
const DEFAULT_SETTINGS: AppearanceSettings = {
  fonte: 'Inter',
  tamanhoFonte: 14,
  palavrasPorSessao: 5000,
  sessionsEnabled: true,
  latexAutomatico: false
};

// Criar contexto
const AppearanceContext = createContext<AppearanceContextType | undefined>(undefined);

// Hook para usar o contexto
export const useAppearance = () => {
  const context = useContext(AppearanceContext);
  if (!context) {
    throw new Error('useAppearance must be used within an AppearanceProvider');
  }
  return context;
};

// Função para obter username do Firestore
const getUsernameFromFirestore = async (userEmail: string): Promise<string> => {
  try {
    const userRef = doc(db, 'usuarios', userEmail);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      return userData.username || userEmail;
    }
    
    return userEmail;
  } catch (error) {
    console.error('Erro ao obter username:', error);
    return userEmail;
  }
};

// Provider do contexto
export const AppearanceProvider = ({ children }: { children: ReactNode }) => {
  const { user } = useAuth();
  const [settings, setSettings] = useState<AppearanceSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);

  // Carregar configurações do Firestore
  const loadSettings = async () => {
    if (!user?.email) {
      setSettings(DEFAULT_SETTINGS);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const username = await getUsernameFromFirestore(user.email);
      const configRef = doc(db, 'usuarios', username, 'configuracoes', 'settings');
      const configDoc = await getDoc(configRef);

      if (configDoc.exists()) {
        const config = configDoc.data();
        if (config.aparencia) {
          setSettings({
            fonte: config.aparencia.fonte || DEFAULT_SETTINGS.fonte,
            tamanhoFonte: config.aparencia.tamanhoFonte || DEFAULT_SETTINGS.tamanhoFonte,
            palavrasPorSessao: config.aparencia.palavrasPorSessao || DEFAULT_SETTINGS.palavrasPorSessao,
            sessionsEnabled: config.aparencia.sessionsEnabled !== undefined ? config.aparencia.sessionsEnabled : DEFAULT_SETTINGS.sessionsEnabled,
            latexAutomatico: config.aparencia.latexAutomatico !== undefined ? config.aparencia.latexAutomatico : DEFAULT_SETTINGS.latexAutomatico
          });
        } else {
          setSettings(DEFAULT_SETTINGS);
        }
      } else {
        setSettings(DEFAULT_SETTINGS);
      }
    } catch (error) {
      console.error('Erro ao carregar configurações de aparência:', error);
      setSettings(DEFAULT_SETTINGS);
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar configurações quando o usuário mudar
  useEffect(() => {
    loadSettings();
  }, [user]);

  // Aplicar configurações às variáveis CSS globais
  useEffect(() => {
    if (!isLoading) {
      const root = document.documentElement;
      root.style.setProperty('--chat-font-family', settings.fonte);
      root.style.setProperty('--chat-font-size', `${settings.tamanhoFonte}px`);
      root.style.setProperty('--chat-words-per-session', settings.palavrasPorSessao.toString());
      root.style.setProperty('--chat-sessions-enabled', settings.sessionsEnabled ? '1' : '0');
      root.style.setProperty('--chat-latex-automatico', settings.latexAutomatico ? '1' : '0');
    }
  }, [settings, isLoading]);

  // Função para atualizar configurações
  const updateSettings = async (newSettings: Partial<AppearanceSettings>) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);

    // Salvar no Firestore automaticamente
    if (user) {
      try {
        const username = await getUsernameFromFirestore(user.email!);
        const configRef = doc(db, 'usuarios', username, 'configuracoes', 'settings');

        // Buscar configurações existentes
        const configDoc = await getDoc(configRef);
        const existingConfig = configDoc.exists() ? configDoc.data() : {};

        // Atualizar apenas a seção de aparência
        await setDoc(configRef, {
          ...existingConfig,
          aparencia: updatedSettings,
          updatedAt: new Date().toISOString()
        }, { merge: true });

        console.log('✅ Configurações de aparência salvas automaticamente');
        console.log('📋 Configurações salvas:', updatedSettings);
      } catch (error) {
        console.error('❌ Erro ao salvar configurações de aparência:', error);
      }
    }
  };

  // Função para aplicar configurações a um elemento específico
  const applyToElement = (element: HTMLElement) => {
    element.style.fontFamily = settings.fonte;
    element.style.fontSize = `${settings.tamanhoFonte}px`;
  };

  // Função para obter variáveis CSS como objeto
  const getCSSVariables = (): Record<string, string> => {
    return {
      '--chat-font-family': settings.fonte,
      '--chat-font-size': `${settings.tamanhoFonte}px`,
      '--chat-words-per-session': settings.palavrasPorSessao.toString()
    };
  };

  const value: AppearanceContextType = {
    settings,
    updateSettings,
    isLoading,
    applyToElement,
    getCSSVariables
  };

  return (
    <AppearanceContext.Provider value={value}>
      {children}
    </AppearanceContext.Provider>
  );
};
